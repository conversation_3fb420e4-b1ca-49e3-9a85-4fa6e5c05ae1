{"name": "reactnativebridgeios", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "postinstall": "patch-package"}, "dependencies": {"@react-native-community/masked-view": "^0.1.11", "@react-native-community/viewpager": "^5.0.11", "@react-native/metro-config": "^0.80.0", "patch-package": "^8.0.0", "react": "18.2.0", "react-native": "0.73.9", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "^2.7.1", "react-native-reanimated": "^3.15.4", "react-native-safe-area-context": "^4.8.0", "react-native-screens": "^3.17.0", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "react-test-renderer": "18.2.0"}, "jest": {"preset": "react-native"}}